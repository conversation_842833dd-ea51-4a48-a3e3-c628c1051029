import { <PERSON>, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useFetch } from '../../../hooks';
import { useQueryClient } from '@tanstack/react-query';
import { getDetailOffer } from '../../../service/offer';
import { DetailOfferMoneyAccountancy, RelatedTransaction } from '../../../types/offer';
import { Col, Row, Spin, TableColumnsType, Typography } from 'antd';
import './styles.scss';
import TableComponent from '../../../components/table';
import { formatCurrency, getFullAddress } from '../../../utilities/shareFunc';
import dayjs from 'dayjs';
import {
  EAPP_STATUS_COLOR,
  EAPP_STATUS_NAME,
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  PAYMENT_METHOD_NAME,
  STATUS_LABELS,
} from '../../../constants/common';
import { OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT } from '../../../configs/path';
import { formatNumber } from '../../../utilities/regex';
import { PaperClipOutlined } from '@ant-design/icons';
import { useEffect, useRef, useCallback } from 'react';

const { Title, Text } = Typography;
const OfferRefundMoneyPaymentDetail: React.FC = ({}) => {
  const { id } = useParams();
  const topRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  const { data: offerOrderData, isRefetching } = useFetch<DetailOfferMoneyAccountancy>({
    api: () => id && getDetailOffer({ id }),
    queryKeyArr: ['get-detail-offer', id],
    enabled: !!id,
    cacheTime: 10,
  });

  useEffect(() => {
    if (isRefetching) {
      topRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [isRefetching]);

  const handleOfferCodeClick = useCallback(
    (recordId: string, event?: React.MouseEvent) => {
      if (recordId === id) {
        event?.preventDefault();

        queryClient.invalidateQueries({
          queryKey: ['get-detail-offer', id],
        });

        topRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    },
    [id, queryClient],
  );

  const offerRefund = offerOrderData?.data?.data;

  const columns: TableColumnsType<RelatedTransaction> = [
    {
      title: 'Dự án',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 107,
      render: (_: string, record: RelatedTransaction) => <Text>{record?.propertyTicket?.project?.name || ''}</Text>,
    },
    {
      title: 'Mã REFUND',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      width: 160,
      render: (_: string, record: RelatedTransaction) => <Text>{record?.propertyTicket?.bookingTicketCode || ''}</Text>,
    },

    {
      title: 'Số sản phẩm',
      dataIndex: 'propertyUnitCode',
      key: 'propertyUnitCode',
      width: 140,
      render: (_: string, record: RelatedTransaction) => (
        <Text>{record?.propertyTicket?.propertyUnit?.code || ''}</Text>
      ),
    },

    {
      title: 'Mã tờ trình',
      dataIndex: 'eappNumber',
      key: 'eappNumber',
      width: 110,
      render: (value: string, record?: RelatedTransaction) => {
        return record?.urlEapp ? (
          <Link to={`${record?.urlEapp}`} target="_blank">
            {value || ''}
          </Link>
        ) : (
          <Text>{value || ''}</Text>
        );
      },
    },

    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 110,
      render: (value: string, record?: RelatedTransaction) => (
        <Link
          to={`${OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT}/${record?.id}`}
          onClick={e => handleOfferCodeClick(record?.id || '', e)}
        >
          {value || ''}
        </Link>
      ),
    },

    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 120,
      align: 'center',
      render: (value: number) => <Text>{formatCurrency(value?.toString()) || ''}</Text>,
    },

    {
      title: 'Trạng thái duyệt',
      dataIndex: 'ticketStatus',
      key: 'ticketStatus',
      align: 'center',
      width: 150,
      render: (value: string) => {
        return <Text style={{ color: EAPP_STATUS_COLOR[value] }}>{EAPP_STATUS_NAME[value]}</Text>;
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: string) => <Text style={{ color: 'black' }}>{STATUS_LABELS[value] || ''}</Text>,
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE) || ''}</Text>,
    },
  ];

  return (
    <Spin spinning={isRefetching}>
      <div ref={topRef}>
        <BreadCrumbComponent titleBread={offerRefund?.code} />
        <Title level={5} style={{ borderBottom: '1px solid rgba(0, 0, 0, 0.06)', padding: '20px 0px' }}>
          Phiếu hoàn {offerRefund?.code}
        </Title>

        <Title level={5} style={{ fontSize: '14px', marginBottom: '16px', marginTop: '30px' }}>
          Thông tin chi tiết
        </Title>
        <div className="customer-info">
          <div className="info-row">
            <span className="info-label">Mã đề nghị:</span>
            <span className="info-value">{offerRefund?.code || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Mã khách hàng:</span>
            <span className="info-value">{offerRefund?.transaction?.propertyTicket?.customer?.code || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Tên khách hàng:</span>
            <span className="info-value">
              {offerRefund?.transaction?.propertyTicket?.customer?.type === 'business'
                ? offerRefund?.transaction?.propertyTicket?.customer?.company?.name
                : offerRefund?.transaction?.propertyTicket?.customer?.personalInfo?.name || ''}
            </span>
          </div>
          <div className="info-row">
            <span className="info-label">Số giấy tờ:</span>
            <span className="info-value">
              {offerRefund?.transaction?.propertyTicket?.customer?.identityNumber || ''}
            </span>
          </div>
          <div className="info-row">
            <span className="info-label">Địa chỉ liên lạc:</span>
            <span className="info-value">
              {offerRefund?.transaction?.propertyTicket?.customer?.type === 'individual'
                ? getFullAddress(offerRefund?.transaction?.propertyTicket?.customer?.info?.rootAddress)
                : getFullAddress(offerRefund?.transaction?.propertyTicket?.customer?.company?.address) || ''}
            </span>
          </div>
          <div className="info-row">
            <span className="info-label">Số tiền:</span>
            <span className="info-value">{formatNumber(offerRefund?.money) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Hình thức thanh toán:</span>
            <span className="info-value">{offerRefund?.state ? PAYMENT_METHOD_NAME[offerRefund?.state] : ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ngày tạo đề nghị hoàn tiền:</span>
            <span className="info-value">{dayjs(offerRefund?.createdAt).format(FORMAT_DATE_TIME) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ngày hoàn tiền:</span>
            <span className="info-value">{dayjs(offerRefund?.date).format(FORMAT_DATE_TIME) || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ghi chú:</span>
            <span className="info-value">{offerRefund?.description || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Ghi chú phiếu YC:</span>
            <span className="info-value">{offerRefund?.transaction?.propertyTicket?.note || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Lí do thanh toán:</span>
            <span className="info-value">{offerRefund?.reason || ''}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Thông tin đính kèm:</span>
            <span className="info-value">
              {Array.isArray(offerRefund?.files) ? (
                <>
                  {offerRefund?.files?.map((file, index) => (
                    <div key={index}>
                      {file?.url ? (
                        <a
                          href={`${import.meta.env.VITE_S3_IMAGE_URL}/${file.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: '#1890ff' }}
                          download={file?.name}
                        >
                          <PaperClipOutlined />
                          {file.name}
                        </a>
                      ) : (
                        <span style={{ color: '#1890ff' }}>{file?.name}</span>
                      )}
                    </div>
                  ))}
                </>
              ) : (
                ''
              )}
            </span>
          </div>
        </div>

        <Title level={5} style={{ fontSize: '14px', marginBottom: '16px', marginTop: '30px' }}>
          Thông tin chung
        </Title>

        <TableComponent
          className="table-offer-refund"
          columns={columns}
          queryKeyArr={['get-offer-refund']}
          dataSource={offerRefund?.relatedTransactions}
          rowKey={'id'}
          isPagination={false}
        />
        <Row
          style={{
            backgroundColor: 'rgba(230, 244, 255, 1)',
            padding: '12px 8px',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            marginTop: '30px',
          }}
        >
          <Col span={8} style={{ textAlign: 'center' }}>
            <span>
              Số tiền: <span style={{ fontWeight: 600 }}>{formatNumber(offerRefund?.totalPendingAmount)}</span>
            </span>
          </Col>
          <Col span={8} style={{ textAlign: 'center' }}>
            <span>
              Đã thanh toán: <span style={{ fontWeight: 600 }}>{formatNumber(offerRefund?.totalPaidAmount)}</span>
            </span>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <span>
              Còn lại: <span style={{ fontWeight: 600 }}>{formatNumber(offerRefund?.totalRemainingAmount)}</span>
            </span>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default OfferRefundMoneyPaymentDetail;
