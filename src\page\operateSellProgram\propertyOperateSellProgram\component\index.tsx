import {
  Button,
  Dropdown,
  MenuProps,
  Modal,
  notification,
  Row,
  Space,
  Spin,
  Tooltip,
  Typography,
  Upload,
  UploadFile,
} from 'antd';
import ExcelJS from 'exceljs';
import FileSaver from 'file-saver';
import { ArrowDownOutlined, ArrowRightOutlined, DownOutlined, UploadOutlined } from '@ant-design/icons';
import {
  DetailProject,
  ExportExcelProject,
  IBlock,
  IPropertyUnit,
  ProjectSaleProgram,
} from '../../../../types/project/project';
import { useEffect, useMemo, useState } from 'react';
import {
  exportPropertyUnit,
  getBlockBySalesProgramIds,
  getListProjectSaleProgram,
  getPropertyUnitByProjectIdAndSalesProgramIds,
  getTemplateImportSP,
  getTemplateUpdateSP,
  importPropertyUnit,
  updatePropertyUnit,
} from '../../../../service/project';
import { useParams } from 'react-router-dom';
import { useCreate<PERSON>ield, useFetch } from '../../../../hooks';
import { RcFile } from 'antd/es/upload';
import CustomTable from './roomTable';
import FilterRowTable from './filterRowTable';
import _ from 'lodash';
import { IMPORT_HISTORY_PROJECT_MANAGEMENT } from '../../../../configs/path';

const templateExportSP = '/assets/Template_ExportSP.xlsx';
interface PropertyUnitProps {
  dataProject?: DetailProject;
}

export interface SalesProgramType {
  id: string;
  name: string;
  code: string;
}

const PropertyUnit: React.FC<PropertyUnitProps> = ({ dataProject }) => {
  const { id: projectID } = useParams<{ id: string }>();
  const [valueSalesProgram, setValueSalesProgram] = useState<string[]>([]);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [actionType, setActionType] = useState<'create' | 'update'>('create');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadedFile, setUploadedFile] = useState<RcFile | null>(null);
  const [open, setOpen] = useState(false);
  const [fileUploaded, setFileUploaded] = useState(false);
  const [valueIdBlock, setValueIdBlock] = useState<string[]>([]);

  const { data: dataSalesProgram, isLoading: isLoadingSalesProgram } = useFetch<ProjectSaleProgram[]>({
    queryKeyArrWithFilter: ['project-sale-program', projectID],
    api: getListProjectSaleProgram,
    moreParams: { projectId: projectID || '' },
  });

  useEffect(() => {
    if (dataSalesProgram?.data?.data?.rows) {
      const defaultIds = dataSalesProgram?.data?.data?.rows.map(item => item.id);
      setValueSalesProgram(defaultIds);
    }
  }, [dataSalesProgram]);

  const {
    data: _dataBlocks,
    isFetching: isPendingCreate,
    isLoading: isLoadingBlock,
  } = useFetch<IBlock[]>({
    queryKeyArr: ['getBlockBySalesProgramIds', valueSalesProgram, valueIdBlock],
    api: getBlockBySalesProgramIds,
    withFilter: false,
    moreParams: { salesProgramIds: valueSalesProgram, blockIds: valueIdBlock },
  });

  const {
    data: dataPropertyUnit,
    refetch,
    isFetching: isLoadingDataTable,
  } = useFetch<IPropertyUnit[]>({
    queryKeyArr: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectID, valueSalesProgram],
    api: () => {
      if (valueSalesProgram.length > 0) {
        return getPropertyUnitByProjectIdAndSalesProgramIds({
          projectId: projectID!,
          salesProgramIds: valueSalesProgram.join(','),
        });
      }
      return Promise.resolve({ data: { data: [] } });
    },
    withFilter: false,
    cacheTime: 1,
  });

  const listPropertyUnit = useMemo(() => {
    const data = dataPropertyUnit?.data?.data || [];
    if (_.isArray(data)) {
      return data;
    }
    return data['rows'] || [];
  }, [dataPropertyUnit]);

  const blocksData = useMemo(() => {
    return _dataBlocks?.data?.data || [];
  }, [_dataBlocks]);
  const transformedBlocks =
    blocksData.map(block => ({
      block: block.code as string, // Đảm bảo `block` luôn là string
      floors: block.floors || [],
      rooms: block.rooms || [],
    })) || [];

  const handleUploadChange = ({ fileList }: { fileList: UploadFile[] }) => {
    if (fileList.length > 0) {
      const file = fileList[0].originFileObj as RcFile;
      const isLtFile = file.size / 1024 / 1024 < 5;
      // Check file type and size
      const isExcel = file.name.endsWith('.xls') || file.name.endsWith('.xlsx');
      if (!isExcel || !isLtFile) {
        fileList[0].status = 'error';
        fileList[0].error = 'File sai định dạng (.xls, .xlsx) hoặc vượt quá 5MB';
      } else {
        fileList[0].status = 'done';
      }
      setUploadedFile(fileList[0].originFileObj as RcFile);
      setFileUploaded(true);
    } else {
      setUploadedFile(null);
      setFileUploaded(false);
    }
    setFileList(fileList);
  };

  const customItemRender = (originNode: React.ReactNode, file: UploadFile<{ customTooltip?: string }>) => {
    return file.error ? <Tooltip title={file.error}>{originNode}</Tooltip> : originNode;
  };

  // eslint-disable-next-line @typescript-eslint/ban-types
  const handleUpload = async (apiCall: Function) => {
    const params = {
      projectId: projectID || '',
      salesProgramId: valueSalesProgram.join(',') || '',
      file: uploadedFile as File,
    };

    const { data } = await apiCall(params);
    handleUploadResult(data?.statusCode || '', data?.historyModel);
  };

  const handleUploadResult = async (
    statusCode: string,
    historyModel?: { description: { line: number; error: string }[] },
  ) => {
    resetUploadState();

    if (statusCode === '0' && historyModel?.description?.length) {
      fileList[0].status = 'error';
      fileList[0].error = `Cập nhật lỗi ${historyModel.description.length} dòng`;
      setFileList([...fileList]);
      return;
    }

    const messages: Record<string, { type: 'error' | 'warning' | 'success'; text: string; hasLink?: boolean }> = {
      PROUNIT0004: { type: 'error', text: 'Upload thất bại, xem tại', hasLink: true },
      PROUNIT0005: { type: 'warning', text: 'Upload thất bại một phần, xem chi tiết tại', hasLink: true },
      PROUNIT0006: { type: 'warning', text: 'Upload đang được xử lý, xem tại', hasLink: true },
      PRJE0013: { type: 'error', text: 'Dự án chưa chọn Đơn vị có thể thu hồi, vui lòng kiểm tra!', hasLink: false },
      '0': { type: 'success', text: 'Hoàn thành upload sản phẩm, xem tại', hasLink: true },
    };

    const msg = messages[statusCode];
    if (msg) {
      const messageContent = msg.hasLink ? (
        <span>
          {msg.text}{' '}
          <a href={IMPORT_HISTORY_PROJECT_MANAGEMENT} target="_blank" rel="noopener noreferrer">
            đây
          </a>
        </span>
      ) : (
        msg.text
      );

      notification[msg.type]({
        message: messageContent,
      });

      if (statusCode === 'PROUNIT0005') {
        await refetch();
      } else if (statusCode === 'PROUNIT0006') {
        setTimeout(async () => {
          await refetch();
        }, 4000);
      }
    }
  };

  const resetUploadState = () => {
    setFileList([]);
    setIsModalVisible(false);
    setUploadedFile(null);
    setFileUploaded(false);
  };

  // Sử dụng cho Import
  const handleImportPropertyUnit = () => handleUpload(callApiImportPropertyUnit);

  // Sử dụng cho Update
  const handleUpdatePropertyUnit = () => handleUpload(callApiUpdatePropertyUnit);

  const { mutateAsync: callApiImportPropertyUnit, isPending: isPendingCallApiImportPropertyUnit } = useCreateField({
    apiQuery: importPropertyUnit,
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectID, valueSalesProgram],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { mutateAsync: callApiUpdatePropertyUnit, isPending: isPendingCallApiUpdatePropertyUnit } = useCreateField({
    apiQuery: updatePropertyUnit,
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectID, valueSalesProgram],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const handleSubmitFilter = (values: Record<string, unknown>) => {
    const valuesSearch = values as { salesProgramIds: string[]; block: { value: string }[] };
    const ids = valuesSearch.salesProgramIds.map((item: string) => item);
    const idsBlockArray = valuesSearch.block?.map(item => item.value?.trim()).filter(value => value);
    setValueSalesProgram(ids);
    setValueIdBlock(idsBlockArray);
  };
  const handleModalOpen = () => {
    setIsModalVisible(true);
    setFileUploaded(false); // Đặt lại trạng thái fileUploaded khi mở modal
  };

  const handleMenuClick: MenuProps['onClick'] = e => {
    if (e.key === '1') {
      setActionType('create');
      handleModalOpen();
    } else if (e.key === '2') {
      setActionType('update');
      handleModalOpen();
    }
  };

  const items: MenuProps['items'] = [
    {
      label: 'Tạo mới',
      key: '1',
    },
    {
      label: 'Cập nhật',
      key: '2',
    },
  ];

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  const clickButton = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (valueSalesProgram.length !== 1) {
      notification.warning({
        message: 'Vui lòng chọn một CTBH cụ thể để mở chức năng này',
      });
    }
  };

  const handleExportExcel = async () => {
    if (valueSalesProgram.length !== 1) {
      notification.warning({
        message: 'Vui lòng chọn một CTBH cụ thể để mở chức năng này',
      });
      return;
    }

    // 1. Tải file template
    const templateResponse = await fetch(templateExportSP);
    if (!templateResponse.ok) {
      throw new Error('Không thể tải template xuất Excel');
    }
    const templateArrayBuffer = await templateResponse.arrayBuffer();

    // 2. Load template bằng ExcelJS
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(templateArrayBuffer);
    const worksheet = workbook.worksheets[0]; // Lấy sheet đầu tiên

    // 3. Lấy dữ liệu từ API
    const response = await exportPropertyUnit({
      projectId: projectID || '',
      salesProgramIds: valueSalesProgram.join(','),
    });

    if (!response?.data?.data || response.data.data.length === 0) {
      notification.warning({
        message: 'Không có dữ liệu để xuất',
      });
      return;
    }

    const data = response.data.data;
    const startRow = 3;
    const templateRow = worksheet.getRow(startRow - 1); // Lấy hàng mẫu để giữ style

    // 4. Ghi dữ liệu mà vẫn giữ nguyên format
    data.forEach((item: ExportExcelProject, index: number) => {
      const rowNumber = startRow + index;
      const row = worksheet.getRow(rowNumber);
      row.height = templateRow.height; // Giữ nguyên chiều cao dòng

      row.getCell(1).value = index + 1; // STT
      row.getCell(2).value = item.code || '';
      row.getCell(3).value = item.name || '';
      row.getCell(4).value = item.shortCode || '';
      row.getCell(5).value = item.apartmentType || '';
      row.getCell(6).value = item.block || '';
      row.getCell(7).value = item.status || '';
      row.getCell(8).value = item.floor || '';
      row.getCell(9).value = item.bedroom || '';
      row.getCell(10).value = item.direction || '';
      row.getCell(11).value = item.view2 || '';
      row.getCell(12).value = item.corner || '';
      row.getCell(13).value = item.insideArea || '';
      row.getCell(14).value = item.area || '';
      row.getCell(15).value = item.outsideArea || '';

      // Định dạng các giá trị tiền tệ
      row.getCell(16).value = item.priceAbove || '';
      row.getCell(17).value = item.priceAboveVat || '';
      row.getCell(18).value = item.price || '';
      row.getCell(19).value = item.priceVat || '';
      row.getCell(20).value = item.housePriceVat || '';
      row.getCell(21).value = item.landPriceVat || '';
      row.getCell(22).value = item.housePrice || '';
      row.getCell(23).value = item.landPrice || '';

      row.getCell(24).value = item.constructStatus || '';
      row.getCell(25).value = item.constructStatusXD || '';

      // Giữ nguyên format từ hàng mẫu
      row.eachCell((cell, colNumber) => {
        const templateCell = templateRow.getCell(colNumber);
        if (templateCell && templateCell.style) {
          cell.style = { ...templateCell.style };

          // Nếu cột chứa số hoặc tiền tệ, đảm bảo format đúng
          if ([16, 17, 18, 19, 20, 21, 22, 23].includes(colNumber)) {
            cell.numFmt = '#,##0';
          }
          if ([4, 8].includes(colNumber)) {
            cell.numFmt = '@'; // '@' định dạng cột là Text trong Excel
          }
        }
      });

      row.commit();
    });

    // 5. Xuất file giữ format
    const buffer = await workbook.xlsx.writeBuffer();
    const fileName = `Danh_sach_can_ho_${dataProject?.code}.xlsx`;
    FileSaver.saveAs(new Blob([buffer], { type: 'application/octet-stream' }), fileName);

    notification.success({
      message: 'Tải về danh sách sản phẩm thành công',
    });
  };
  const handleGetTemplateImportSP = async () => {
    try {
      const response = await getTemplateImportSP({
        projectId: projectID || '',
        saleProgramId: valueSalesProgram[0],
      });

      const fileUrl = response?.data?.data?.fileUrl;
      if (fileUrl) {
        const fullUrl = `${import.meta.env.VITE_S3_IMAGE_URL}/${fileUrl}`;
        window.open(fullUrl, '_blank');
      } else {
        notification.error({
          message: 'Không tìm thấy file Template',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Không thể tải template',
      });
    }
  };
  const handleGetTemplateUpdateSP = async () => {
    try {
      const response = await getTemplateUpdateSP({
        projectId: projectID || '',
        saleProgramId: valueSalesProgram[0],
      });

      const fileUrl = response?.data?.data?.fileUrl;
      if (fileUrl) {
        const fullUrl = `${import.meta.env.VITE_S3_IMAGE_URL}/${fileUrl}`;
        window.open(fullUrl, '_blank');
      } else {
        notification.error({
          message: 'Không tìm thấy file Template',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Không thể tải template',
      });
    }
  };

  return (
    <div className="PropertyUnit">
      <Row justify={'space-between'}>
        <FilterRowTable handleSubmit={handleSubmitFilter} defaultSelectedIds={valueSalesProgram} />
        <div className="button-container">
          <Button onClick={handleExportExcel}>Xuất dữ liệu</Button>
          <Dropdown
            menu={menuProps}
            trigger={['click']}
            open={open}
            onOpenChange={isOpen => {
              if (valueSalesProgram.length === 1) {
                setOpen(isOpen);
              } else {
                setOpen(false);
              }
            }}
          >
            <Button onClick={clickButton}>
              <Space>
                Nhập sản phẩm
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
      </Row>
      <Modal
        onCancel={() => {
          setIsModalVisible(false);
          setFileList([]);
          setUploadedFile(null);
          setFileUploaded(false);
        }}
        className="importPropertyUnitModal"
        title="Import"
        open={isModalVisible}
        footer={[
          <Button
            onClick={() => {
              actionType === 'create' ? handleImportPropertyUnit() : handleUpdatePropertyUnit();
            }}
            disabled={fileList.length === 0 || fileList[0].status === 'error'}
            key="submit"
            type="primary"
            loading={isPendingCallApiImportPropertyUnit || isPendingCallApiUpdatePropertyUnit}
          >
            Nhập dữ liệu
          </Button>,
        ]}
      >
        <Space className="spaceImport" direction="vertical">
          <Typography.Text>Upload file theo mẫu để thêm sản phẩm vào bảng hàng</Typography.Text>
          <Typography.Text>
            Nếu bạn chưa có file excel template mẫu, vui lòng tải tại &nbsp;
            <Typography.Link
              onClick={async () => {
                if (actionType === 'create') {
                  await handleGetTemplateImportSP();
                } else {
                  await handleGetTemplateUpdateSP();
                }
              }}
            >
              đây
            </Typography.Link>
          </Typography.Text>
          <div>
            <Upload
              onChange={handleUploadChange}
              fileList={fileList}
              listType="picture"
              beforeUpload={() => false}
              accept=".xlsx,.xls"
              maxCount={1}
              itemRender={customItemRender}
              showUploadList={{ showRemoveIcon: true, showPreviewIcon: false }}
            >
              {!fileUploaded && (
                <Button
                  disabled={isPendingCallApiImportPropertyUnit || isPendingCallApiUpdatePropertyUnit}
                  className="buttonImportPropertyUnit"
                  icon={<UploadOutlined />}
                >
                  Upload
                </Button>
              )}
            </Upload>
          </div>
        </Space>
      </Modal>

      <div className="info-floor">
        <ArrowDownOutlined /> <span className="text-arrow">Tầng</span>
        <ArrowRightOutlined /> <span className="text-arrow">Số căn hộ</span>
      </div>
      <div className="scroll-table">
        <Spin spinning={isLoadingBlock}>
          <CustomTable
            listBlocks={transformedBlocks}
            listPropertyUnit={listPropertyUnit}
            isPendingCreate={isPendingCreate || isLoadingSalesProgram || isLoadingDataTable}
          />
        </Spin>
      </div>
    </div>
  );
};
export default PropertyUnit;
