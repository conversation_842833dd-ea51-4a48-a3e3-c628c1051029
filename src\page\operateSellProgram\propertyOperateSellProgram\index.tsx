import {
  Button,
  Dropdown,
  MenuProps,
  Modal,
  notification,
  Row,
  Space,
  Spin,
  Tooltip,
  Typography,
  Upload,
  UploadFile,
} from 'antd';
import ExcelJS from 'exceljs';
import FileSaver from 'file-saver';
import {
  ArrowDownOutlined,
  ArrowRightOutlined,
  DollarCircleOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  FormOutlined,
  TableOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { RcFile } from 'antd/es/upload';
import TableListPropertyUnit from './tableListPropertyUnit';
import FilterRowTableOperate from './filterRowTableOperate';
import FormModalUnitConversion from './popupUnitConversion/modalUnitConversion';
import {
  DetailProject,
  ExportExcelProject,
  IBlock,
  IPropertyUnit,
  ProjectSaleProgram,
} from '../../../types/project/project';
import { useSellProgramStore } from '../../Store';
import { useCreateField, useFetch } from '../../../hooks';
import { IMPORT_HISTORY_PROJECT_MANAGEMENT } from '../../../configs/path';
import CustomTable from './component/roomTable';
import {
  exportPropertyUnit,
  getBlockBySalesProgramIds,
  getListProjectSaleProgram,
  getPropertyUnitByProjectIdAndSalesProgramIds,
  getTemplateImportSP,
  getTemplateUpdateSP,
  importPropertyUnit,
  updatePropertyUnit,
} from '../../../service/project';
import _ from 'lodash';

const templateExportSP = '/assets/Template_UpdateSP.xlsx';
interface PropertyUnitProps {
  dataProject?: DetailProject;
}

export interface SalesProgramType {
  id: string;
  name: string;
  code: string;
}

const PropertyOperateSellProgram: React.FC<PropertyUnitProps> = ({ dataProject }) => {
  const { id: projectId } = useParams<{ id: string }>();
  const [valueSalesProgram, setValueSalesProgram] = useState<string[]>([]);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [isUnitConversionModalVisible, setIsUnitConversionModalVisible] = useState(false);
  const [actionType, setActionType] = useState<'create' | 'update'>('create');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadedFile, setUploadedFile] = useState<RcFile | null>(null);
  const [open, setOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [fileUploaded, setFileUploaded] = useState(false);
  const [valueIdBlock, setValueIdBlock] = useState<string[]>([]);
  const [isProductActionsMenuOpen, setOpenProductActionMenu] = useState(false);
  const toggleImportModal = (isVisible: boolean) => setIsImportModalVisible(isVisible);
  const toggleModal = (isVisible: boolean) => setIsUnitConversionModalVisible(isVisible);
  const SetProjectId = useSellProgramStore(state => state.SetProjectId);
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);
  const [listStatus, setListStatus] = useState<any>([]);

  const { data: dataSalesProgram, isLoading: isLoadingSalesProgram } = useFetch<ProjectSaleProgram[]>({
    queryKeyArr: ['project-sale-program', projectId],
    api: getListProjectSaleProgram,
    moreParams: { projectId: projectId || '' },
  });

  useEffect(() => {
    SetProjectId?.(projectId || '');
    setValueSalesProgram(salesProgramIds);
  }, [SetProjectId, projectId, salesProgramIds]);
  useEffect(() => {
    if (salesProgramIds) {
      // Nếu có selectedSaleProgramId, đặt nó làm giá trị mặc định
      setValueSalesProgram(salesProgramIds);
    } else if (dataSalesProgram?.data?.data?.rows) {
      // Nếu không có, sử dụng danh sách mặc định từ API
      const defaultIds = dataSalesProgram?.data?.data?.rows.map(item => item.id);
      setValueSalesProgram(defaultIds);
    }
  }, [dataSalesProgram, salesProgramIds]);

  const {
    data: _dataBlocks,
    isFetching: isPendingCreate,
    isLoading: isLoadingBlock,
  } = useFetch<IBlock[]>({
    queryKeyArr: ['getBlockBySalesProgramIds', valueSalesProgram, valueIdBlock],
    api: getBlockBySalesProgramIds,
    withFilter: false,
    moreParams: { salesProgramIds: valueSalesProgram, blockIds: valueIdBlock },
  });

  const {
    data: dataPropertyUnit,
    refetch,
    isLoading: isLoadingDataTable,
  } = useFetch<IPropertyUnit[]>({
    queryKeyArr: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, valueSalesProgram, listStatus],
    api: () => {
      if (valueSalesProgram) {
        return getPropertyUnitByProjectIdAndSalesProgramIds({
          projectId: projectId!,
          salesProgramIds: valueSalesProgram.join(','),
          primaryStatus: listStatus,
        });
      }
    },
    withFilter: false,
    cacheTime: 1,
  });

  const listPropertyUnit = useMemo(() => {
    const data = dataPropertyUnit?.data?.data || [];
    if (_.isArray(data)) {
      return data;
    }
    return data['rows'] || [];
  }, [dataPropertyUnit]);

  const blocksData = useMemo(() => {
    return _dataBlocks?.data?.data || [];
  }, [_dataBlocks]);
  const transformedBlocks =
    blocksData.map(block => ({
      block: block.code as string, // Đảm bảo `block` luôn là string
      floors: block.floors || [],
      rooms: block.rooms || [],
    })) || [];

  const handleUploadChange = ({ fileList }: { fileList: UploadFile[] }) => {
    if (fileList.length > 0) {
      const file = fileList[0].originFileObj as RcFile;
      const isLtFile = file.size / 1024 / 1024 < 5;
      // Check file type and size
      const isExcel = file.name.endsWith('.xls') || file.name.endsWith('.xlsx');
      if (!isExcel || !isLtFile) {
        fileList[0].status = 'error';
        fileList[0].error = 'File sai định dạng (.xls, .xlsx) hoặc vượt quá 5MB';
      } else {
        fileList[0].status = 'done';
      }
      setUploadedFile(fileList[0].originFileObj as RcFile);
      setFileUploaded(true);
    } else {
      setUploadedFile(null);
      setFileUploaded(false);
    }
    setFileList(fileList);
  };

  const customItemRender = (originNode: React.ReactNode, file: UploadFile<{ customTooltip?: string }>) => {
    return file.error ? <Tooltip title={file.error}>{originNode}</Tooltip> : originNode;
  };

  // eslint-disable-next-line @typescript-eslint/ban-types
  const handleUpload = async (apiCall: Function) => {
    const params = {
      projectId: projectId || '',
      salesProgramId: valueSalesProgram.join(',') || '',
      file: uploadedFile as File,
    };

    const { data } = await apiCall(params);
    handleUploadResult(data?.statusCode || '', data?.historyModel);
  };

  const handleUploadResult = async (
    statusCode: string,
    historyModel?: { description: { line: number; error: string }[] },
  ) => {
    resetUploadState();

    if (statusCode === '0' && historyModel?.description?.length) {
      fileList[0].status = 'error';
      fileList[0].error = `Cập nhật lỗi ${historyModel.description.length} dòng`;
      setFileList([...fileList]);
      return;
    }

    const messages: Record<string, { type: 'error' | 'warning' | 'success'; text: string; hasLink?: boolean }> = {
      PROUNIT0004: { type: 'error', text: 'Upload thất bại, xem tại', hasLink: true },
      PROUNIT0005: { type: 'warning', text: 'Upload thất bại một phần, xem chi tiết tại', hasLink: true },
      PROUNIT0006: { type: 'warning', text: 'Upload đang được xử lý, xem tại', hasLink: true },
      PRJE0013: { type: 'error', text: 'Dự án chưa chọn Đơn vị có thể thu hồi, vui lòng kiểm tra!', hasLink: false },
      '0': { type: 'success', text: 'Hoàn thành upload sản phẩm, xem tại', hasLink: true },
    };

    const msg = messages[statusCode];
    if (msg) {
      const messageContent = msg.hasLink ? (
        <span>
          {msg.text}{' '}
          <a href={IMPORT_HISTORY_PROJECT_MANAGEMENT} target="_blank" rel="noopener noreferrer">
            đây
          </a>
        </span>
      ) : (
        msg.text
      );

      notification[msg.type]({
        message: messageContent,
      });

      if (statusCode === 'PROUNIT0005') {
        await refetch();
      } else if (statusCode === 'PROUNIT0006') {
        setTimeout(async () => {
          await refetch();
        }, 4000);
      }
    }
  };

  const resetUploadState = () => {
    setFileList([]);
    setIsImportModalVisible(false);
    setUploadedFile(null);
    setFileUploaded(false);
  };

  // Sử dụng cho Import
  const handleImportPropertyUnit = () => handleUpload(callApiImportPropertyUnit);

  // Sử dụng cho Update
  const handleUpdatePropertyUnit = () => handleUpload(callApiUpdatePropertyUnit);

  const { mutateAsync: callApiImportPropertyUnit, isPending: isPendingCallApiImportPropertyUnit } = useCreateField({
    apiQuery: importPropertyUnit,
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, valueSalesProgram],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { mutateAsync: callApiUpdatePropertyUnit, isPending: isPendingCallApiUpdatePropertyUnit } = useCreateField({
    apiQuery: updatePropertyUnit,
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, valueSalesProgram],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const handleSubmitFilter = (values: Record<string, unknown>) => {
    const valuesSearch = values as { block: { value: string }[]; primaryStatus: { value: string }[] };
    const idsBlockArray = valuesSearch.block?.map(item => item.value?.trim()).filter(value => value);
    setListStatus(valuesSearch?.primaryStatus);
    setValueIdBlock(idsBlockArray);
    setValueSalesProgram(salesProgramIds);
  };
  const handleModalOpen = () => {
    toggleImportModal(true);
    setFileUploaded(false);
  };

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
  };

  const handleMenuClick: MenuProps['onClick'] = e => {
    if (e.key === '1') {
      setActionType('create');
      handleModalOpen();
    } else if (e.key === '2') {
      setActionType('update');
      handleModalOpen();
    }
  };

  const items: MenuProps['items'] = [
    {
      label: 'Tạo mới',
      key: '1',
    },
    {
      label: 'Cập nhật',
      key: '2',
    },
  ];

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  const clickButton = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (valueSalesProgram.length !== 1) {
      notification.warning({
        message: 'Vui lòng chọn một CTBH cụ thể để mở chức năng này',
      });
    }
  };

  const handleExportExcel = async () => {
    if (valueSalesProgram.length !== 1) {
      notification.warning({
        message: 'Vui lòng chọn một CTBH cụ thể để mở chức năng này',
      });
      return;
    }

    // 1. Tải file template
    const templateResponse = await fetch(templateExportSP);
    if (!templateResponse.ok) {
      throw new Error('Không thể tải template xuất Excel');
    }
    const templateArrayBuffer = await templateResponse.arrayBuffer();

    // 2. Load template bằng ExcelJS
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(templateArrayBuffer);
    const worksheet = workbook.worksheets[0]; // Lấy sheet đầu tiên

    // 3. Lấy dữ liệu từ API
    const response = await exportPropertyUnit({
      projectId: projectId || '',
      salesProgramIds: valueSalesProgram.join(','),
    });

    if (!response?.data?.data || response.data.data.length === 0) {
      notification.warning({
        message: 'Không có dữ liệu để xuất',
      });
      return;
    }

    const data = response.data.data;
    const startRow = 3;
    const templateRow = worksheet.getRow(startRow - 1); // Lấy hàng mẫu để giữ style

    // 4. Ghi dữ liệu mà vẫn giữ nguyên format
    data.forEach((item: ExportExcelProject, index: number) => {
      const rowNumber = startRow + index;
      const row = worksheet.getRow(rowNumber);
      row.height = templateRow.height; // Giữ nguyên chiều cao dòng

      row.getCell(1).value = index + 1; // STT
      row.getCell(2).value = item.code || '';
      row.getCell(3).value = item.name || '';
      row.getCell(4).value = item.shortCode || '';
      row.getCell(5).value = item.apartmentType || '';
      row.getCell(6).value = item.block || '';
      row.getCell(7).value = item.status || '';
      row.getCell(8).value = item.floor || '';
      row.getCell(9).value = item.bedroom || '';
      row.getCell(10).value = item.direction || '';
      row.getCell(11).value = item.view2 || '';
      row.getCell(12).value = item.corner || '';
      row.getCell(13).value = item.insideArea || '';
      row.getCell(14).value = item.area || '';
      row.getCell(15).value = item.outsideArea || '';

      // Định dạng các giá trị tiền tệ
      row.getCell(16).value = item.priceAbove || '';
      row.getCell(17).value = item.priceAboveVat || '';
      row.getCell(18).value = item.price || '';
      row.getCell(19).value = item.priceVat || '';
      row.getCell(20).value = item.housePriceVat || '';
      row.getCell(21).value = item.landPriceVat || '';
      row.getCell(22).value = item.housePrice || '';
      row.getCell(23).value = item.landPrice || '';

      row.getCell(24).value = item.constructStatus || '';
      row.getCell(25).value = item.constructStatusXD || '';

      // Giữ nguyên format từ hàng mẫu
      row.eachCell((cell, colNumber) => {
        const templateCell = templateRow.getCell(colNumber);
        if (templateCell && templateCell.style) {
          cell.style = { ...templateCell.style };

          // Nếu cột chứa số hoặc tiền tệ, đảm bảo format đúng
          if ([16, 17, 18, 19, 20, 21, 22, 23].includes(colNumber)) {
            cell.numFmt = '#,##0';
          }
          if ([4, 8].includes(colNumber)) {
            cell.numFmt = '@'; // '@' định dạng cột là Text trong Excel
          }
        }
      });

      row.commit();
    });

    // 5. Xuất file giữ format
    const buffer = await workbook.xlsx.writeBuffer();
    const fileName = `Danh_sach_can_ho_${dataProject?.code}.xlsx`;
    FileSaver.saveAs(new Blob([buffer], { type: 'application/octet-stream' }), fileName);

    notification.success({
      message: 'Tải về danh sách sản phẩm thành công',
    });
  };

  const handleProductActionsClick: MenuProps['onClick'] = e => {
    if (e.key === '1') {
      toggleModal(true);
    } else if (e.key === '2') {
      notification.info({ message: 'Thu hồi sản phẩm' });
    } else if (e.key === '3') {
      notification.info({ message: 'Xóa sản phẩm' });
    }
  };

  const productActionsItems: MenuProps['items'] = [
    {
      label: 'Chuyển sản phẩm',
      key: '1',
    },
    {
      label: 'Thu hồi sản phẩm',
      key: '2',
    },
    {
      label: 'Xóa sản phẩm',
      key: '3',
    },
  ];

  const productActionsMenuProps = {
    items: productActionsItems,
    onClick: handleProductActionsClick,
  };
  const handleGetTemplateImportSP = async () => {
    try {
      const response = await getTemplateImportSP({
        projectId: projectId || '',
        saleProgramId: valueSalesProgram[0],
      });

      const fileUrl = response?.data?.data?.fileUrl;
      if (fileUrl) {
        const fullUrl = `${import.meta.env.VITE_S3_IMAGE_URL}/${fileUrl}`;
        window.open(fullUrl, '_blank');
      } else {
        notification.error({
          message: 'Không tìm thấy file Template',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Không thể tải template',
      });
    }
  };
  const handleGetTemplateUpdateSP = async () => {
    try {
      const response = await getTemplateUpdateSP({
        projectId: projectId || '',
        saleProgramId: valueSalesProgram[0],
      });

      const fileUrl = response?.data?.data?.fileUrl;
      if (fileUrl) {
        const fullUrl = `${import.meta.env.VITE_S3_IMAGE_URL}/${fileUrl}`;
        window.open(fullUrl, '_blank');
      } else {
        notification.error({
          message: 'Không tìm thấy file Template',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Không thể tải template',
      });
    }
  };
  return (
    <div className="PropertyUnit">
      <Row justify={'space-between'}>
        <FilterRowTableOperate handleSubmit={handleSubmitFilter} />
        <div className="button-container">
          <Dropdown
            menu={productActionsMenuProps}
            trigger={['click']}
            open={isProductActionsMenuOpen}
            onOpenChange={isOpen => {
              if (valueSalesProgram.length === 1) {
                setOpenProductActionMenu(isOpen);
              } else {
                setOpenProductActionMenu(false);
              }
            }}
          >
            <Button onClick={clickButton} icon={<FormOutlined />}>
              <Space>
                Quản lý sản phẩm
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>

          <Button icon={<DollarCircleOutlined />}>Quản lý giá</Button>
          <Button icon={<ExclamationCircleOutlined />}>Quản lý ưu tiên</Button>
          <Button onClick={handleExportExcel}>Xuất dữ liệu</Button>
          <Dropdown
            menu={menuProps}
            trigger={['click']}
            open={open}
            onOpenChange={isOpen => {
              if (valueSalesProgram.length === 1) {
                setOpen(isOpen);
              } else {
                setOpen(false);
              }
            }}
          >
            <Button onClick={clickButton}>
              <Space>
                Nhập sản phẩm
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
      </Row>
      <Modal
        onCancel={() => {
          toggleImportModal(false);
          setFileList([]);
          setUploadedFile(null);
          setFileUploaded(false);
        }}
        className="importPropertyUnitModal"
        title="Import"
        open={isImportModalVisible}
        footer={[
          <Button
            onClick={() => {
              actionType === 'create' ? handleImportPropertyUnit() : handleUpdatePropertyUnit();
            }}
            disabled={fileList.length === 0 || fileList[0].status === 'error'}
            key="submit"
            type="primary"
            loading={isPendingCallApiImportPropertyUnit || isPendingCallApiUpdatePropertyUnit}
          >
            Nhập dữ liệu
          </Button>,
        ]}
      >
        <Space className="spaceImport" direction="vertical">
          <Typography.Text>Upload file theo mẫu để thêm sản phẩm vào bảng hàng</Typography.Text>
          <Typography.Text>
            Nếu bạn chưa có file excel template mẫu, vui lòng tải tại &nbsp;
            <Typography.Link
              onClick={async () => {
                if (actionType === 'create') {
                  await handleGetTemplateImportSP();
                } else {
                  await handleGetTemplateUpdateSP();
                }
              }}
            >
              đây
            </Typography.Link>
          </Typography.Text>
          <div>
            <Upload
              onChange={handleUploadChange}
              fileList={fileList}
              listType="picture"
              beforeUpload={() => false}
              accept=".xlsx,.xls"
              maxCount={1}
              itemRender={customItemRender}
              showUploadList={{ showRemoveIcon: true, showPreviewIcon: false }}
            >
              {!fileUploaded && (
                <Button
                  disabled={isPendingCallApiImportPropertyUnit || isPendingCallApiUpdatePropertyUnit}
                  className="buttonImportPropertyUnit"
                  icon={<UploadOutlined />}
                >
                  Upload
                </Button>
              )}
            </Upload>
          </div>
        </Space>
      </Modal>

      <div className="info-floor">
        <Button
          icon={<ExclamationCircleOutlined />}
          onClick={() => handleViewModeChange('list')}
          className={viewMode === 'list' ? 'active' : ''}
        />
        <Button
          icon={<TableOutlined />}
          onClick={() => handleViewModeChange('grid')}
          className={viewMode === 'grid' ? 'active' : ''}
        />
        <ArrowDownOutlined /> <span className="text-arrow">Tầng</span>
        <ArrowRightOutlined /> <span className="text-arrow">Số căn hộ</span>
      </div>
      {viewMode === 'grid' ? (
        <div className="scroll-table">
          <Spin spinning={isLoadingBlock || isLoadingDataTable}>
            <CustomTable
              listBlocks={transformedBlocks}
              listPropertyUnit={listPropertyUnit}
              isPendingCreate={isPendingCreate || isLoadingSalesProgram}
            />
          </Spin>
        </div>
      ) : (
        <TableListPropertyUnit data={listPropertyUnit} />
      )}
      <FormModalUnitConversion visible={isUnitConversionModalVisible} onClose={() => toggleModal(false)} />
    </div>
  );
};
export default PropertyOperateSellProgram;
