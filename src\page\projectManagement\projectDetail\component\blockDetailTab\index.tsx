import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Button, Input, Modal, Form, Table, Space, InputNumber, Select } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import EditTag from './tag';
import { useParams } from 'react-router-dom';
import { returnNumericValue } from '../../../../../components/validation';
import AddBlock from './addBlock';
import { createBlock, deleteBlock, getBlockByProjectId, updateBlock } from '../../../../../service/project';
import { useCreateField, useDeleteField, useFetch, useUpdateField } from '../../../../../hooks';
import { IBlock } from '../../../../../types/project/project';
import InptNameblock from './inptNameblock';
import { TYPE_BLOCK } from '../../../../../constants/common';

interface Floor {
  id: string;
  name: string;
}
interface Room {
  id: string;
  name: string;
}

interface Block {
  id: string;
  name: string;
  productsPerFloor: number;
  floors: Floor[];
  rooms: Room[];
  functionalAreaTypeCode?: string;
  functionalAreaTypeName?: string;
}

const BlockInfo: React.FC = () => {
  const { id: projectID } = useParams<{ id: string }>();
  // const [blockData, setBlockData] = useState<Block[]>([]);

  const { mutateAsync: handleCreateBlock } = useCreateField({
    apiQuery: createBlock,
    keyOfDetailQuery: ['getBlockByProjectId', projectID],
    label: 'block',
    isMessageError: false,
  });

  const { mutateAsync: _deleteBlock } = useDeleteField({
    apiQuery: deleteBlock,
    keyOfDetailQuery: ['getBlockByProjectId', projectID],
    label: 'block',
  });

  const { mutateAsync: _updateBlock } = useUpdateField({
    apiQuery: updateBlock,
    keyOfDetailQuery: ['getBlockByProjectId', projectID],
    label: 'block',
  });

  const handleAddBlocks = async (updatedBlocks: Block) => {
    const paramsCratedBlock = {
      projectId: projectID,
      block: updatedBlocks.name,
      floors: updatedBlocks.floors.map(floor => floor.name),
      rooms: updatedBlocks.rooms.map(room => room.name),
    };
    await handleCreateBlock(paramsCratedBlock);
  };

  const { data: dataBlocks, isFetching: isPendingCreate } = useFetch<IBlock[]>({
    queryKeyArr: ['getBlockByProjectId', projectID],
    api: () => getBlockByProjectId({ idProject: projectID }),
    withFilter: false,
  });

  const blockData = useMemo(() => {
    return dataBlocks?.data?.data?.map((block: IBlock) => ({
      id: block.id,
      name: block.block,
      productsPerFloor: block?.floors?.length,
      floors: block?.floors?.map((iten: string) => ({
        id: uuidv4(),
        name: iten,
      })),
      rooms: block?.rooms?.map((iten: string) => ({
        id: uuidv4(),
        name: iten,
      })),
      functionalAreaTypeCode: block?.functionalAreaTypeCode,
      functionalAreaTypeName: block?.functionalAreaTypeName,
      code: block?.code,
    }));
  }, [dataBlocks]);

  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleModalAddFBlock = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          setIsModalOpen(!isModalOpen);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      form.resetFields();
      setIsModalOpen(!isModalOpen);
    }
  }, [form, isModalOpen]);

  const handleAddBlock = () => {
    form.validateFields().then(values => {
      const { name, numberOfFloors, productsPerFloor } = values;
      if (!name.trim()) {
        form.setFields([
          {
            name: 'name',
            errors: ['Vui lòng nhập tên block'],
          },
        ]);
        return;
      }
      const floors: Floor[] = [];
      const rooms: Room[] = [];
      for (let i = 1; i <= numberOfFloors; i++) {
        floors.push({
          id: uuidv4(),
          name: i.toString().padStart(3, '0'),
        });
      }
      for (let i = 1; i <= productsPerFloor; i++) {
        rooms.push({
          id: uuidv4(),
          name: i.toString().padStart(3, '0'),
        });
      }

      const newBlock: Block = {
        id: uuidv4(),
        name,
        productsPerFloor,
        floors,
        rooms,
      };

      handleAddBlocks(newBlock);

      setIsModalOpen(!isModalOpen);
      form.resetFields();
    });
  };

  const handleRemoveFloor = (tagKey: string, idRow: string) => {
    const _blockData = [...((blockData as Block[]) || [])];
    const block = _blockData.find(block => {
      if (block.id === idRow) {
        block.floors = block.floors.filter(floor => floor.id !== tagKey);
        block.rooms = block.rooms.filter(room => room.id !== tagKey);
        return block;
      }
    });
    if (block) {
      const paramsUpdateBlock = {
        id: idRow,
        projectId: projectID,
        block: block.name,
        floors: block.floors.map(floor => floor.name),
        rooms: block.rooms.map(room => room.name),
      };
      _updateBlock(paramsUpdateBlock);
    }
  };

  // const handleDeleteBlock = (id: string) => {
  //   Modal.confirm({
  //     title: 'Xác nhận xóa block',
  //     content: 'Bạn có chắc muốn xóa block này không',
  //     cancelText: 'Quay lại',
  //     onOk: () => {
  //       _deleteBlock(id);
  //     },
  //     okButtonProps: {
  //       type: 'default',
  //     },
  //     cancelButtonProps: {
  //       type: 'primary',
  //     },
  //   });
  // };

  const handleEditBlock = async (tagKey: string, idRow: string, newName: string, label: string) => {
    if (!newName.trim()) {
      return;
    }
    const _blockData = [...((blockData as Block[]) || [])];
    const block = _blockData.find(block => {
      if (block.id === idRow) {
        if (label === 'floors') {
          block.floors = block.floors.map(floor => {
            if (floor.id === tagKey) {
              return { ...floor, name: newName };
            }
            return floor;
          });
        } else {
          block.rooms = block.rooms.map(room => {
            if (room.id === tagKey) {
              return { ...room, name: newName };
            }
            return room;
          });
        }
        return block;
      }
    });
    if (block) {
      const paramsUpdateBlock = {
        id: idRow,
        projectId: projectID,
        block: block.name,
        floors: block.floors.map(floor => floor.name),
        rooms: block.rooms.map(room => room.name),
      };
      await _updateBlock(paramsUpdateBlock);
    }
  };

  const updateMasterBlock = (idRow: string, value: string, label: string) => {
    if (!value.trim()) {
      return;
    }
    const _blockData = [...((blockData as Block[]) || [])];
    const block = _blockData.find(block => {
      if (block.id === idRow) {
        if (label === 'floors') {
          block.floors.push({
            id: uuidv4(),
            name: value,
          });
        } else {
          block.rooms.push({
            id: uuidv4(),
            name: value,
          });
        }
        return block;
      }
    });
    if (block) {
      const paramsUpdateBlock = {
        id: idRow,
        projectId: projectID,
        block: block.name,
        floors: block.floors.map(floor => floor.name),
        rooms: block.rooms.map(room => room.name),
      };
      _updateBlock(paramsUpdateBlock);
    }
  };

  const handleUpdateBlockData = (value: string, rowId: string, column: string) => {
    if (!value.trim()) {
      console.log('value', blockData);
      return;
    }
    const _blockData = [...((blockData as Block[]) || [])];
    const block = _blockData.find(block => {
      if (block.id === rowId) {
        if (column === 'name') {
          block.name = value as string;
        }
        return block;
      }
    });
    if (block) {
      const paramsUpdateBlock = {
        id: rowId,
        projectId: projectID,
        block: block.name,
        floors: block.floors.map(floor => floor.name),
        rooms: block.rooms.map(room => room.name),
      };
      _updateBlock(paramsUpdateBlock);
    }
  };

  const columns = [
    {
      title: 'Block',
      dataIndex: 'code',
      key: 'code',
      width: '10%',
      render: (code: string, record: Block) => (
        <InptNameblock handleUpdateBlockData={handleUpdateBlockData} rowId={record.id} name={code} />
      ),
    },
    {
      title: 'Loại bất động sản',
      dataIndex: 'functionalAreaTypeCode',
      key: 'functionalAreaTypeCode',
      width: '10%',
      render: (functionalAreaTypeCode: string, _: Block) => (
        <Select disabled={true} value={functionalAreaTypeCode || ''} style={{ width: 100 }} options={TYPE_BLOCK} />
      ),
    },
    {
      title: 'Tầng / đường',
      dataIndex: 'floors',
      key: 'floors',
      width: '40%',
      render: (floors: Floor[], record: Block) => (
        <Space wrap>
          {
            <AddBlock
              idRow={record.id}
              data={floors}
              projectId={projectID}
              updateBlock={_updateBlock}
              label="floors"
              updateMasterBlock={updateMasterBlock}
            />
          }
          {floors.map(floor => (
            <EditTag
              idRow={record.id}
              key={`${floor.id}`}
              tagKey={`${floor.id}`}
              value={floor.name}
              label="floors"
              onEdit={handleEditBlock}
              onRemove={handleRemoveFloor}
            >
              {floor.name}
            </EditTag>
          ))}
        </Space>
      ),
    },
    {
      title: 'Số sản phẩm tối đa trên mặt bằng',
      dataIndex: 'rooms',
      key: 'rooms',
      width: '40%',
      render: (rooms: Room[], record: Block) => (
        <Space wrap>
          {
            <AddBlock
              idRow={record.id}
              data={rooms}
              projectId={projectID}
              updateBlock={_updateBlock}
              label="rooms"
              updateMasterBlock={updateMasterBlock}
            />
          }
          {rooms.map(room => (
            <EditTag
              idRow={record.id}
              key={`${room.id}`}
              tagKey={`${room.id}`}
              label="rooms"
              value={room.name}
              onEdit={handleEditBlock}
              onRemove={handleRemoveFloor}
            >
              {room.name}
            </EditTag>
          ))}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Table
        dataSource={blockData as undefined}
        columns={columns}
        rowKey="id"
        pagination={false}
        loading={isPendingCreate}
        bordered
      />
      {/* <ButtonOfPageDetail
        handleSubmit={handleSubmitBlock}
        handleCancel={handleCancel}
        loadingSubmit={isPendingCreate}
      /> */}

      <Modal
        title="Thêm block"
        open={isModalOpen}
        onCancel={handleModalAddFBlock}
        // onOk={handleAddBlock}
        footer={[
          <Button key="submit" type="primary" onClick={handleAddBlock}>
            Thêm block
          </Button>,
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Tên block"
            name="name"
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập tên block',
              },
            ]}
          >
            <Input placeholder="Nhập tên block" maxLength={100} />
          </Form.Item>
          <Form.Item
            label="Số lượng tầng trong block"
            name="numberOfFloors"
            rules={[{ required: true, message: 'Vui lòng nhập số lượng tầng' }]}
          >
            <InputNumber onKeyDown={returnNumericValue} placeholder="Nhập số lượng tầng" min={0} max={200} />
          </Form.Item>
          <Form.Item
            label="Số sản phẩm tối đa / tầng"
            name="productsPerFloor"
            rules={[{ required: true, message: 'Vui lòng nhập số sản phẩm / tầng' }]}
          >
            <InputNumber onKeyDown={returnNumericValue} placeholder="Nhập số sản phẩm / tầng" min={0} max={200} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BlockInfo;
